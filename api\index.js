import request from "@/utils/request";
import { URL_CODE_MAP } from "../static/urlCode";
import { writeLog, LogLevel } from "@/utils/logger";
import { filterEmptyFields } from "@/utils/common";

// 通用 post 请求方法
const post = (url, urlCode, data, params) => {
  let userInfo = JSON.parse(sessionStorage.getItem("getUserInfo"));
  const defaultParams = {
    userName: userInfo?.loginName,
  };

  // 过滤空值字段
  const filteredData = filterEmptyFields({
    ...data,
    userName: userInfo?.loginName,
  });

  const filteredParams = filterEmptyFields({
    ...defaultParams,
    ...params,
  });

  return request({
    url,
    method: "post",
    data: filteredData,
    params: filteredParams,
    urlCode,
  });
};

// 退出方法
export function logout() {
  const url = "/logout";
  return post(url, URL_CODE_MAP[url], {});
}

// 获取用户详细信息
export function getInfo() {
  const url = "/h5/getInfo";
  let userInfo = JSON.parse(sessionStorage.getItem("getUserInfo") || "{}");
  const userName =
    process.env.NODE_ENV === "development"
      ? (
          userInfo.loginName ||
          sessionStorage.getItem("loginName") ||
          "admin"
        ).split(",")[0]
      : userInfo.loginName.split(",")[0];
  return post(url, URL_CODE_MAP[url], { userName });
}

// 查询表单列表信息
export function getFormListInformation(data) {
  const url = "/form/get/list";
  return post(url, URL_CODE_MAP[url], data);
}

// 查询自主填报(带分页版本)
export function getFormListByPage(data) {
  const url = "/form/get/self/list/page";
  return post(url, URL_CODE_MAP[url], data);
}
// 查询自主填报列表
export function getFormList() {
  const url = "/form/get/self/list";
  return post(url, URL_CODE_MAP[url]);
}

export function getQrcode(data) {
  const url = "/scan/code/get";
  return post(url, URL_CODE_MAP[url], data);
}

export function getHistoryInfoList(data) {
  const url = "/scan/code/get/history/form";
  return post(url, URL_CODE_MAP[url], data);
}
//用不上的api
export function getForm(id) {
  const url = "/form/get";
  const data = {
    formId: id,
  };
  return post(url, URL_CODE_MAP[url], data);
}

// 查询表单详情
export function getFormDetail(formId, formDataId) {
  const url = "/form/get";
  const data = {
    formId,
    formDataId,
  };
  return post(url, URL_CODE_MAP[url], data);
}

// 派单的表单信息提交接口
export function submitForm(data) {
  const url = "/form/submit";
  return post(url, URL_CODE_MAP[url], data);
}

// 获取自主填报表单
export function getDefaultForm(data) {
  const url = "/scan/code/get/default/form";
  return post(url, URL_CODE_MAP[url], data);
}

// 获取数据看板数据
export function getDashboardData(id) {
  const url = `/result/kan/ban/get`;
  const data = {
    type: id,
  };
  return post(url, URL_CODE_MAP[url], data);
}

// 表单上传图片
export function saveImages(data) {
  const url = "/form/update/file";
  return post(url, URL_CODE_MAP[url], data);
}

// 客户环境登陆
export function loginEnvironment(data) {
  const url = "/h5/login";
  return post(url, URL_CODE_MAP[url], data);
}

// 默认工单不提交返回就调用撤销接口
export function revokeForm(data) {
  const url = "/form/revoke";

  // 添加日志
  writeLog(LogLevel.INFO, "revokeForm", "调用撤销接口", {
    url,
    urlCode: URL_CODE_MAP[url],
    requestData: data,
  });

  return post(url, URL_CODE_MAP[url], data);
}

// 获取客户列表
export function getCustomerList(data) {
  const url = "/scan/code/get/custom/info";
  return post(url, URL_CODE_MAP[url], data);
}

// 无单填报获取默认表单
export function getDefaultFormByNoOrder(data) {
  const url = "/scan/code/get/self/form";
  return post(url, URL_CODE_MAP[url], data);
}

// 无单填报提交
export function submitFormByNoOrder(data) {
  const url = "/form/self/submit";
  return post(url, URL_CODE_MAP[url], data);
}

// 上传图片
export function uploadImage(data) {
  const url = "/common/h5/upload";
  return post(
    url,
    URL_CODE_MAP[url],
    {
      file: data.base64Data,
    },
    {}
  );
}

// 获取经纬度信息
export function getLatitudeAndLongitude(data) {
  const url = "/form/getLatitudeAndLongitude";
  return post(url, URL_CODE_MAP[url], data);
}

// 获取服务履责表单类型列表
export function getFormTypeList(data) {
  const url = "/form/get/formType/list";
  return post(url, URL_CODE_MAP[url], data);
}

